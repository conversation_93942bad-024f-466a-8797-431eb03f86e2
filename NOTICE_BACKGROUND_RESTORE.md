# 通知背景图恢复修改说明

## 修改概述
在 `ProductsCertificationViewController.m` 中恢复了通知cell的本地背景图 `cell_bg_notice`，保持认证项背景图从网络加载的同时，确保通知区域使用本地资源。

## 修改详情

### 文件位置
`Nano_Loan/Certification/ProductsCertificationViewController.m`

### 修改内容
**修改前**（使用半透明白色背景）：
```objc
// 通知cell - 移除本地背景图依赖，改为纯色背景或接口控制
UIView *noticeCell = [[UIView alloc] initWithFrame:CGRectMake(8, 12, cardWidth-16, noticeHeight)];
noticeCell.backgroundColor = [UIColor colorWithRed:255/255.0 green:255/255.0 blue:255/255.0 alpha:0.8]; // 半透明白色背景
noticeCell.layer.cornerRadius = 12;
noticeCell.layer.masksToBounds = YES;

// 可添加通知icon/label等内容，如果后续需要通知背景图也可通过接口下发
// TODO: 如果需要通知区域的背景图，可以在接口中添加对应字段，类似认证项的iconURL
```

**修改后**（使用本地背景图）：
```objc
// 通知cell - 使用本地背景图 cell_bg_notice
UIView *noticeCell = [[UIView alloc] initWithFrame:CGRectMake(8, 12, cardWidth-16, noticeHeight)];
noticeCell.layer.cornerRadius = 12;
noticeCell.layer.masksToBounds = YES;

// 添加本地背景图
UIImageView *noticeBgImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"cell_bg_notice"]];
noticeBgImageView.frame = noticeCell.bounds;
noticeBgImageView.contentMode = UIViewContentModeScaleAspectFill;
[noticeCell addSubview:noticeBgImageView];

// 可添加通知icon/label等内容
```

## 技术实现

### 1. 移除纯色背景
- 删除了 `backgroundColor` 设置
- 保留了圆角和裁剪设置

### 2. 添加背景图片视图
- 创建 `UIImageView` 加载本地图片 `cell_bg_notice`
- 设置 `frame` 为 `noticeCell.bounds` 确保完全覆盖
- 使用 `UIViewContentModeScaleAspectFill` 保持图片比例并填充

### 3. 图片资源确认
- 图片位置：`Nano_Loan/Assets.xcassets/Authentication/cell_bg_notice.imageset/`
- 图片文件：`Frame 1171276764.png`
- 配置文件：`Contents.json`

## 设计逻辑

### 混合策略
- **通知cell**：使用本地背景图 `cell_bg_notice`（固定设计）
- **认证项cell**：使用网络背景图（动态内容，通过 `iconURL` 字段控制）

### 原因分析
1. **通知区域**：设计相对固定，使用本地资源可以确保加载速度和一致性
2. **认证项区域**：内容动态变化，需要根据不同产品/状态显示不同背景

## 视觉效果

- 通知cell现在会显示设计师提供的专用背景图
- 保持了与认证项的视觉区分
- 确保了UI的完整性和设计一致性

## 注意事项

1. **图片资源**：确保 `cell_bg_notice` 图片已正确添加到项目中
2. **性能**：本地图片加载速度快，不会影响页面渲染性能
3. **维护**：如需更换通知背景，只需替换 `cell_bg_notice` 图片资源

## 测试建议

1. **基础显示**：验证通知cell背景图正确显示
2. **圆角效果**：确认背景图遵循12pt圆角设置
3. **布局适配**：测试不同屏幕尺寸下的显示效果
4. **与认证项对比**：确认通知cell与认证项cell的视觉区分明显
